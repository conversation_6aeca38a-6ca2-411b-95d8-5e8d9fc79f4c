import React, { useState } from 'react';
import './ProductCard.css';

const ProductCard = ({ product, onAddToCart }) => {
  const [imageError, setImageError] = useState(false);
  const {
    id,
    name,
    image,
    price,
    originalPrice,
    discount,
    rating,
    reviews,
    features
  } = product;

  const discountPercentage = originalPrice ? Math.round(((originalPrice - price) / originalPrice) * 100) : 0;

  return (
    <div className="product-card">
      <div className="product-image">
        <img
          src={imageError ? `https://via.placeholder.com/400x400/f0f0f0/666666?text=${encodeURIComponent(name.split(' ')[0])}` : image}
          alt={name}
          onError={() => setImageError(true)}
          loading="lazy"
        />
        <div className="wishlist-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </div>
      </div>
      
      <div className="product-info">
        <h3 className="product-name">{name}</h3>
        
        <div className="rating-section">
          <div className="rating">
            <span className="rating-value">{rating}</span>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="#fff">
              <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
            </svg>
          </div>
          <span className="reviews">({reviews.toLocaleString()})</span>
        </div>
        
        <div className="features">
          {features.slice(0, 2).map((feature, index) => (
            <span key={index} className="feature">{feature}</span>
          ))}
        </div>
        
        <div className="price-section">
          <span className="current-price">₹{price.toLocaleString()}</span>
          {originalPrice && (
            <>
              <span className="original-price">₹{originalPrice.toLocaleString()}</span>
              <span className="discount">{discountPercentage}% off</span>
            </>
          )}
        </div>
        
        <div className="delivery-info">
          <span className="free-delivery">Free delivery</span>
        </div>
      </div>
      
      <button 
        className="add-to-cart-btn"
        onClick={() => onAddToCart(product)}
      >
        Add to Cart
      </button>
    </div>
  );
};

export default ProductCard;
