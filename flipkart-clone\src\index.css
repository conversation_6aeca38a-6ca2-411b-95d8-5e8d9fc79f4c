* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color: #212121;
  background-color: #f1f3f6;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f1f3f6;
}

#root {
  width: 100%;
  min-height: 100vh;
}

a {
  color: #2874f0;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  border: none;
  cursor: pointer;
  font-family: inherit;
  transition: all 0.2s ease;
}

button:focus {
  outline: 2px solid #2874f0;
  outline-offset: 2px;
}

button:focus-visible {
  outline: 2px solid #2874f0;
  outline-offset: 2px;
}

input {
  font-family: inherit;
  border: none;
  outline: none;
}

input:focus {
  outline: 2px solid #2874f0;
  outline-offset: -2px;
}
