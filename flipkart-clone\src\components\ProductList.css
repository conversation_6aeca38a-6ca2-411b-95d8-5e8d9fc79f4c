.product-list {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.products-header h2 {
  font-size: 20px;
  font-weight: 500;
  color: #212121;
  margin: 0;
}

.products-count {
  font-size: 14px;
  color: #878787;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.no-products {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px 20px;
}

.no-products-content {
  text-align: center;
  max-width: 300px;
}

.no-products-content svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-products-content h3 {
  font-size: 18px;
  color: #212121;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.no-products-content p {
  font-size: 14px;
  color: #878787;
  margin: 0;
  line-height: 1.4;
}

@media (max-width: 1024px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .product-list {
    padding: 16px 12px;
  }
  
  .products-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .products-header h2 {
    font-size: 18px;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 8px;
  }
}
