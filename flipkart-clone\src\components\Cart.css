.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  justify-content: flex-end;
}

.cart-sidebar {
  background: white;
  width: 400px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
}

.cart-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #2874f0;
  color: white;
}

.cart-header h2 {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-cart svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-cart h3 {
  font-size: 18px;
  color: #212121;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.empty-cart p {
  font-size: 14px;
  color: #878787;
  margin: 0;
}

.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.cart-item {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.cart-item-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.cart-item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cart-item-name {
  font-size: 14px;
  font-weight: 400;
  color: #212121;
  margin: 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.cart-item-price {
  font-size: 14px;
  font-weight: 500;
  color: #212121;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-btn {
  background: #f0f0f0;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: #212121;
}

.quantity-btn:hover {
  background: #e0e0e0;
}

.quantity {
  font-size: 14px;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

.cart-item-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.item-total {
  font-size: 14px;
  font-weight: 500;
  color: #212121;
}

.remove-btn {
  background: none;
  border: none;
  color: #878787;
  cursor: pointer;
  font-size: 12px;
  text-decoration: underline;
}

.remove-btn:hover {
  color: #ff6161;
}

.cart-footer {
  border-top: 1px solid #e0e0e0;
  padding: 16px 20px;
  background: #fafafa;
}

.cart-total {
  margin-bottom: 16px;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #212121;
}

.total-amount {
  font-size: 18px;
  font-weight: 600;
}

.checkout-btn {
  width: 100%;
  background-color: #fb641b;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.checkout-btn:hover {
  background-color: #e55a1b;
}

@media (max-width: 480px) {
  .cart-sidebar {
    width: 100vw;
  }
}
