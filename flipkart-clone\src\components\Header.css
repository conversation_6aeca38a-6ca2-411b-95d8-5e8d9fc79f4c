.header {
  background-color: #2874f0;
  color: white;
  padding: 8px 0;
  box-shadow: 0 1px 1px 0 rgba(0,0,0,.16);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  gap: 16px;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 120px;
}

.logo h1 {
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  font-style: italic;
}

.tagline {
  font-size: 11px;
  font-style: italic;
  margin-top: -2px;
}

.plus {
  color: #ffe500;
  font-weight: 600;
}

.search-container {
  flex: 1;
  max-width: 500px;
  position: relative;
  display: flex;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 2px 0 0 2px;
  font-size: 14px;
  outline: none;
}

.search-input::placeholder {
  color: #878787;
}

.search-btn {
  background-color: #fff;
  border: none;
  padding: 12px 16px;
  border-radius: 0 2px 2px 0;
  cursor: pointer;
  color: #2874f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover {
  background-color: #f0f0f0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 24px;
}

.login-btn {
  background-color: #fff;
  color: #2874f0;
  border: none;
  padding: 8px 24px;
  border-radius: 2px;
  font-weight: 500;
  cursor: pointer;
  font-size: 14px;
}

.login-btn:hover {
  background-color: #f0f0f0;
}

.more-menu {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.more-menu:hover {
  opacity: 0.8;
}

.cart-btn {
  background: none;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  position: relative;
}

.cart-btn:hover {
  opacity: 0.8;
}

.cart-count {
  background-color: #ff6161;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  position: absolute;
  top: -8px;
  right: -8px;
}

@media (max-width: 768px) {
  .header-container {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .search-container {
    order: 3;
    width: 100%;
    max-width: none;
  }
  
  .header-actions {
    gap: 16px;
  }
  
  .more-menu {
    display: none;
  }
}
