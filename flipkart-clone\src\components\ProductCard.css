.product-card {
  background: white;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.12);
  transition: box-shadow 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-image {
  position: relative;
  text-align: center;
  margin-bottom: 12px;
  background-color: #fafafa;
  border-radius: 8px;
  padding: 16px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.3s ease;
  background-color: #fff;
  background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.wishlist-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0,0,0,0.12);
  color: #878787;
}

.wishlist-btn:hover {
  color: #ff6161;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-name {
  font-size: 14px;
  font-weight: 400;
  color: #212121;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.rating-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating {
  background-color: #388e3c;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  font-weight: 500;
}

.rating-value {
  font-size: 12px;
}

.reviews {
  font-size: 12px;
  color: #878787;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.feature {
  font-size: 12px;
  color: #878787;
  line-height: 1.3;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 4px;
}

.current-price {
  font-size: 16px;
  font-weight: 500;
  color: #212121;
}

.original-price {
  font-size: 14px;
  color: #878787;
  text-decoration: line-through;
}

.discount {
  font-size: 12px;
  color: #388e3c;
  font-weight: 500;
}

.delivery-info {
  margin-top: 4px;
}

.free-delivery {
  font-size: 12px;
  color: #388e3c;
  font-weight: 500;
}

.add-to-cart-btn {
  background-color: #ff9f00;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 12px;
  transition: background-color 0.2s ease;
}

.add-to-cart-btn:hover {
  background-color: #e68900;
}

.add-to-cart-btn:active {
  transform: translateY(1px);
}

@media (max-width: 768px) {
  .product-card {
    padding: 12px;
  }

  .product-image {
    padding: 12px;
  }

  .product-image img {
    height: 150px;
  }

  .product-name {
    font-size: 13px;
  }

  .current-price {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .product-image {
    padding: 8px;
  }

  .product-image img {
    height: 120px;
  }
}
